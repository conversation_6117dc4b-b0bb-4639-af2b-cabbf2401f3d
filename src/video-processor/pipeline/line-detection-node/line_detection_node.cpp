#include "./line_detection_node.h"
#include "../../helpers/helpers.h"
#include "../../video_processor_configs.h"


#include "./devtools/data_exporter.hpp"

namespace IQVideoProcessor::Pipeline {

std::vector<TFloat> syncPositionsGraphic;
std::vector<TFloat> syncGraphic;

LineDetectionNode::LineDetectionNode(const SampleRateType sampleRate) : sampleRate_(sampleRate), segmentSyncDetectionFilter_(sampleRate) {
  // Initialize default sync detection parameters
  processingConfig_.pulseMaxWidth = static_cast<TFloat>(sampleRate_) * static_cast<TFloat>(PULSE_MAX_WIDTH_US); // 500 microseconds
  processingConfig_.pulseMaxSearchDistance = static_cast<TFloat>(sampleRate_) / MIN_LINE_RATE_HZ; // ~66.7 microseconds
  detectedVideoSyncPulses_.resize(1000); // Preallocate for efficiency
  setRunning();
}

LineDetectionNode::~LineDetectionNode() {
  PipelineComponent::stop();
}

bool LineDetectionNode::process(DemodulatedSegment& segment) {
  if (!running()) return false;

  auto& filteredSegment = segmentSyncDetectionFilter_.process(segment);
  detectSegmentVideoSyncPulses(filteredSegment);

  // <DEBUGGING>
  if (syncPositionsGraphic.size() != segment.totalSamples) {
    syncPositionsGraphic.resize(segment.totalSamples);
    syncGraphic.resize(segment.totalSamples);
  }
  std::fill(syncPositionsGraphic.begin(), syncPositionsGraphic.end(), 0);
  for (const auto & detected_video_sync_pulse : detectedVideoSyncPulses_) {
    // auto centerPos = static_cast<uint32_t>(detected_video_sync_pulse.centerPosition) + segment.effectiveOffset - segment.effectiveStartPosition;
    auto fallingPos = static_cast<uint32_t>(detected_video_sync_pulse.fallingFrontPosition) + segment.effectiveOffset;
    auto risingPos = static_cast<uint32_t>(detected_video_sync_pulse.risingFrontPosition) + segment.effectiveOffset;
    syncPositionsGraphic[fallingPos] = -35;
    syncPositionsGraphic[risingPos] = -35;
  }
  DevTools::export_debug_data<TFloat>("LDN", "fronts", segment.segmentIndex, syncPositionsGraphic.data(), syncPositionsGraphic.size());
  DevTools::export_debug_data<TFloat>("LDN", "sync", segment.segmentIndex, syncGraphic.data(), syncGraphic.size());
  DevTools::export_debug_data<TFloat>("LDN", "original", segment.segmentIndex, segment.data.data(), segment.totalSamples);
  DevTools::export_debug_data<TFloat>("LDN", "ave500kHz", filteredSegment.segmentIndex, filteredSegment.data.data(), filteredSegment.totalSamples);
  // </DEBUGGING>

  const auto& standardDetectionResult = videoStandardDetector_.processSegmentSyncPulses(detectedVideoSyncPulses_);
  if (standardDetectionResult.status == VideoStandardDetector::DETECTION_IN_PROGRESS) {
    return running();
  }

  if (standardDetectionResult.status == VideoStandardDetector::DETECTION_FAILED) {
    if (!standardDetectorRetryCountdown_.running()) {
      standardDetectorRetryCountdown_.reset();
    }
    // Waiting for ~1 second before retrying to detect the standard again
    if (standardDetectorRetryCountdown_.tick(filteredSegment.effectiveSamples)) {
      videoStandardDetector_.reset();
      standardDetectorRetryCountdown_.stop(); // just in case
    }
    return running();
  }



  if (standardDetectionResult.status == VideoStandardDetector::DETECTION_COMPLETE) {
    // Successfully detected the standard, no further action needed
    standardDetectorRetryCountdown_.stop(); // Stop further retries

    // Initialize VideoSyncPulsePredictor if not already done
    if (!predictorInitialized_) {
      initializeSyncPulsePredictor(standardDetectionResult);
    }

    // Process detected pulses with the predictor
    if (syncPulsePredictor_ && !detectedVideoSyncPulses_.empty()) {
      syncPulsePredictor_->processPulses(detectedVideoSyncPulses_);
    }
  }


  return running();
}

LineDetectionNode::SegmentProcessingConfig& LineDetectionNode::getSegmentProcessingConfig(const AveFilteredDemodulatedSegment &segment) {
  const auto [minSampleValue, maxSampleValue] = Helpers::getMinMax(&segment.data[segment.effectiveOffset], segment.effectiveSamples);

  auto& config = processingConfig_;
  config.signalMinVal = minSampleValue;
  config.signalMaxVal = maxSampleValue;
  config.signalScale = maxSampleValue - minSampleValue;
  // Configure sync detection parameters based on the signal range and segment properties
  config.pulseThresholdTrigValueDelta = config.signalScale * static_cast<TFloat>(0.10); // 10% of the signal range
  config.pulseThresholdLength = static_cast<TFloat>(segment.aveSize) / static_cast<TFloat>(2); // Half of the average size of a filter
  config.pulseSyncValue = minSampleValue + config.signalScale * static_cast<TFloat>(0.18); // 18% of the signal range
  return config;
}

void LineDetectionNode::detectSegmentVideoSyncPulses(const AveFilteredDemodulatedSegment &filteredSegment) {
  detectedVideoSyncPulses_.clear(); // Clear previous detections

  const auto spc = getSegmentProcessingConfig(filteredSegment);
  const auto absolutePosition = static_cast<TFloat>(filteredSegment.effectiveStartPosition);

  // <DEBUGGING>
  std::fill(syncGraphic.begin(), syncGraphic.end(), spc.pulseSyncValue);
  // </DEBUGGING>

  VideoSyncDetector videoSyncDetector(
    &filteredSegment.data[filteredSegment.effectiveOffset],
    // Getting as many as we can, minus half the filter size to ensure we operate within valid data
    filteredSegment.effectiveSamples + filteredSegment.effectiveOffset - filteredSegment.halfAveSize - 1,
    spc
  );

  TFloat fromPosition = nextSegmentFromPosition_; // Continue from last position
  const auto toPosition = static_cast<TFloat>(filteredSegment.effectiveSamples);
  while (videoSyncDetector.findNext(fromPosition)) {
    const auto& result = videoSyncDetector.getResult();
    detectedVideoSyncPulses_.push_back(VideoSyncPulse::fromSyncResult(result, absolutePosition));
    fromPosition = result.risingFrontPosition; // Move to the end of the detected pulse for next search

    if (fromPosition >= toPosition) {
      nextSegmentFromPosition_ = fromPosition - toPosition; // Store overflow for next segment
      return;
    }
  }
  nextSegmentFromPosition_ = 0; // Reset if we haven't found anything within this segment
}

void LineDetectionNode::initializeSyncPulsePredictor(const VideoStandardDetector::Result& standardResult) {
  IQVideoProcessor::VideoSyncPulsePredictor::Config config;
  config.sampleRate = sampleRate_;
  config.standardResult = standardResult;
  config.callback = [this](const IQVideoProcessor::VideoSyncPulsePredictor::EventData& eventData) {
    handlePredictorEvent(eventData);
  };

  syncPulsePredictor_ = std::make_unique<IQVideoProcessor::VideoSyncPulsePredictor>(config);
  predictorInitialized_ = true;
}

int32_t lastLineCount = -1;

void LineDetectionNode::handlePredictorEvent(const IQVideoProcessor::VideoSyncPulsePredictor::EventData& eventData) {
  // Handle different event types from the predictor
  switch (eventData.type) {
    case IQVideoProcessor::VideoSyncPulsePredictor::FRAME_BEGIN:
      // Frame begins - could be used for downstream processing initialization
      // For now, just log or handle as needed
      break;

    case IQVideoProcessor::VideoSyncPulsePredictor::EQUALIZATION_PULSE:
      // Equalization pulse detected - could be used for timing validation
      break;

    case IQVideoProcessor::VideoSyncPulsePredictor::LINE_DETECTED:
      // Line detected - this is where we would extract video line data
      // The eventData contains lineNumber, dataStartPosition, and sampleCount
      // This could be used to segment the video data for downstream processing
      lastLineCount = eventData.lineNumber;
      break;

    case IQVideoProcessor::VideoSyncPulsePredictor::EMPTY_LINE:
      // Empty line detected - handle missing line data
      break;

    case IQVideoProcessor::VideoSyncPulsePredictor::FRAME_END:
      // Frame ends - could trigger frame processing completion
      break;
  }
}


} // namespace IQVideoProcessor::Pipeline
