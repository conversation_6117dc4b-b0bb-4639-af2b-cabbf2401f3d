#pragma once

#include "./partials/segment-ave-filter.h"
#include "./partials/video-sync-detector.h"
#include "./partials/video_standard_detector.h"
#include "./partials/video_sync_pulse_predictor.h"
#include "../iq_demodulation_node_types.h"
#include "../../utils/countdown/countdown.h"
#include "../../../stream-pipeline/stream_node.h"
#include "../../../types.h"

#include <memory>
#include <functional>

namespace IQVideoProcessor::Pipeline {

class LineDetectionNode final : public SPipeline::StreamNode<DemodulatedSegment, DemodulatedSegment> {
public:
  explicit LineDetectionNode(SampleRateType sampleRate);
  ~LineDetectionNode() override;

private:
  bool process(DemodulatedSegment& segment) override;

  struct SegmentProcessingConfig: VideoSyncDetector::Config {
    TFloat signalMinVal;
    TFloat signalMaxVal;
    TFloat signalScale;
  };

  SampleRateType sampleRate_;
  SegmentAveFilter segmentSyncDetectionFilter_;
  SegmentProcessingConfig processingConfig_{};
  SignalRangeEstimator signalRangeEstimator_{sampleRate_};
  VideoStandardDetector videoStandardDetector_{sampleRate_};
  Countdown<> standardDetectorRetryCountdown_{sampleRate_};

  std::vector<VideoSyncPulse> detectedVideoSyncPulses_{}; // Contains detected pulses for the current segment, cleared on each new segment
  TFloat nextSegmentFromPosition_{0};

  // Video sync pulse prediction and validation system
  std::unique_ptr<IQVideoProcessor::VideoSyncPulsePredictor> syncPulsePredictor_;
  bool predictorInitialized_{false};

  SegmentProcessingConfig& getSegmentProcessingConfig(const AveFilteredDemodulatedSegment &segment);
  void detectSegmentVideoSyncPulses(const AveFilteredDemodulatedSegment &filteredSegment);

  // VideoSyncPulsePredictor callback handler
  void handlePredictorEvent(const IQVideoProcessor::VideoSyncPulsePredictor::EventData& eventData);

  // Initialize predictor when standard detection is complete
  void initializeSyncPulsePredictor(const IQVideoProcessor::VideoStandardDetector::Result& standardResult);
};

} // namespace IQVideoProcessor::Pipeline
