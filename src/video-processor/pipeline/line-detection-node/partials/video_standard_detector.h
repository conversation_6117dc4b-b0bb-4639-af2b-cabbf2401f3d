#pragma once

#include "../../../types.h"
#include "../../../../types.h"
#include "./signal_range_estimator.h"
#include "./video_sync_pulse.h"

#include <vector>

namespace IQVideoProcessor {


class VideoStandardDetector {
public:
  enum DetectionStatus {
    DETECTION_IN_PROGRESS = 0,
    DETECTION_COMPLETE = 10,
    DETECTION_FAILED = 20,
  };

  struct Result {
    VideoStandard standard{STANDARD_UNKNOWN};
    DetectionStatus status{DETECTION_IN_PROGRESS};
    TFloat horizontalFrequencyHz{0};      // ≈ 15625 or ≈ 15734
    TFloat fieldRateHz{0};                // ≈ 50 or ≈ 59.94
    TFloat frameRate{0};                  // FPS = fieldRateHz / 2 (if available)
    TFloat horizontalLineDuration{0};     // samples per line (measured)
    uint32_t linesPerFrame{0};            // 625 or 525

    // Absolute min/max pulse widths (in seconds) across discovered sets
    TFloat hSyncWidthMinSec{0};
    TFloat hSyncWidthMaxSec{0};
    TFloat eqPulseWidthMinSec{0};
    TFloat eqPulseWidthMaxSec{0};

    // Counts around the vertical interval
    uint32_t eqPreCount{0};               // equalizing pulses before the vertical interval (best V run)
    uint32_t eqPostCount{0};              // equalizing pulses after the vertical interval (best V run)
    uint32_t verticalPulseCount{0};       // pulses in the longest vertical run (typically 5 or 6)
  };

  explicit VideoStandardDetector(SampleRateType sampleRate);

  [[nodiscard]] Result& processSegmentSyncPulses(const std::vector<VideoSyncPulse>& syncPulses);

  void reset();

private:
  enum VideoSyncPulseType {
    UNKNOWN = 0,
    HORIZONTAL_SYNC_PULSE = 10,
    EQUALIZING_PULSE = 20,
    VERTICAL_SYNC_PULSE = 30,
    VERTICAL_SYNC_LONG_PULSE = 35,
  };

  SampleRateType sampleRate_;
  Result result_;
  size_t giveUpIterations_{0};
  std::vector<VideoSyncPulse> collectedSyncPulses_;
  std::vector<VideoSyncPulse> discoveredHorizontalPulses_;
  std::vector<VideoSyncPulse> discoveredVerticalPulses_;
  std::vector<VideoSyncPulse> discoveredEqualizingPulses_;

  SignalRangeEstimator signalRangeEstimator_{sampleRate_};

  void discoverValidSyncPulses();
  void analyzeDiscoveredPulses();

  [[nodiscard]] VideoSyncPulseType detectCurrentPulseType(const VideoSyncPulse &prevPulse, const VideoSyncPulse &currentPulse, const VideoSyncPulse &nextPulse) const;
};

} // namespace IQVideoProcessor