#include "./video_sync_state_machine.h"

namespace IQVideoProcessor {
VideoSyncStateMachine::VideoSyncStateMachine(const SampleRateType sampleRate): sampleRate_(sampleRate) {

}

inline void VideoSyncStateMachine::setNextState(const State nextState) {
  if (nextState != currentState_) {
    previousState_ = currentState_;
    currentState_ = nextState;
  }
}

inline void VideoSyncStateMachine::setPreviousPulse(const VideoSyncPulse& pulse, VideoSyncPulseType const pulseType) {
  previousPulse_ = pulse;
  previousPulseType_ = pulseType;
}

inline VideoSyncStateMachine::VideoSyncPulseType VideoSyncStateMachine::estimatePulseType(const VideoSyncPulse& pulse) const {
  switch (rangeEstimator_.estimatePulseByWidth(pulse.width)) {
    case EstimatedPulseType::HORIZONTAL_OR_EQUALIZING_PULSE: return VideoSyncPulseType::HORIZONTAL_OR_EQUALIZING_PULSE;
    case EstimatedPulseType::VERTICAL_PULSE: return VideoSyncPulseType::VERTICAL_PULSE;
    case EstimatedPulseType::VERTICAL_LONG_PULSE: return VideoSyncPulseType::VERTICAL_LONG_PULSE;
    case EstimatedPulseType::UNKNOWN_PULSE:
    default: return VideoSyncPulseType::UNKNOWN_PULSE;
  }
}

void VideoSyncStateMachine::initialize(const VideoStandardDetector::Result& detectedStandard, const EventCallback& eventCallback) {
  ds_ = detectedStandard;
  eventCallback_ = eventCallback;
  if (ds_.standard == VideoStandard::STANDARD_UNKNOWN) {
    return;
  }
  setNextState(WAITING_FIRST_SYNC_PULSE_STATE);
}

void VideoSyncStateMachine::reset() {
  previousState_ = UNKNOWN_STATE;
  currentState_ = UNKNOWN_STATE;
  previousPulse_ = {};
}

void VideoSyncStateMachine::processNextSyncPulse(const VideoSyncPulse& pulse) {
  const auto pulseType = estimatePulseType(pulse);

  switch (currentState_) {
    case WAITING_FIRST_SYNC_PULSE_STATE: return handleWaitingFirstSyncPulse(pulse, pulseType);
    case SYNCING_IN_PROGRESS_STATE: return handleSyncingInProgress(pulse, pulseType);
    case UNKNOWN_STATE:
    break;
  }
}

inline void VideoSyncStateMachine::handleWaitingFirstSyncPulse(const VideoSyncPulse& pulse, const VideoSyncPulseType pulseType) {
  if (pulseType == UNKNOWN_PULSE) {
    return;
  }
  setPreviousPulse(pulse, pulseType);
  setNextState(SYNCING_IN_PROGRESS_STATE);
}

inline void VideoSyncStateMachine::handleSyncingInProgress(const VideoSyncPulse& pulse, VideoSyncPulseType pulseType) {
  if (pulseType == UNKNOWN_PULSE) { // Invalid pulse or distance, lets find another first pulse
    setNextState(WAITING_FIRST_SYNC_PULSE_STATE);
  }

  const auto distance = pulse.absCenterPosition - previousPulse_.absCenterPosition;
  const auto distanceType = rangeEstimator_.estimateSignalDistance(distance);

  if (distanceType == HORIZONTAL_DISTANCE && pulseType == HORIZONTAL_OR_EQUALIZING_PULSE) {
    // Having pulse, followed by horizontal distance after any other pulse, consider it horizontal
    return enterProcessingHorizontalRegionState(pulse);
  }
  //
  if (distanceType == HALF_HORIZONTAL_DISTANCE && pulseType == HORIZONTAL_OR_EQUALIZING_PULSE) {
    // Having pulse, followed by half horizontal distance after any other pulse, consider it equalizing
    return enterProcessingEqualizingSyncRegionState(pulse);
  }
  if (distanceType == HALF_HORIZONTAL_DISTANCE && pulseType == VERTICAL_PULSE) {
    // Having a vertical pulse, followed by half horizontal distance after the previous pulse,
    // consider it vertical somewhere in the middle of ver
    return enterProcessingVerticalSyncRegionState(pulse, previousPulseType_ != VERTICAL_PULSE);
  }
  if (distanceType == _70PERCENT_HORIZONTAL_DISTANCE && pulseType == VERTICAL_PULSE) {
    // Having a vertical pulse, followed by ~70% horizontal distance after the previous pulse,
    // consider it first vertical pulse right after the last equalizing pulse
    return enterProcessingVerticalSyncRegionState(pulse, true);
  }
  if (distanceType == _33PERCENT_HORIZONTAL_DISTANCE && pulseType == HORIZONTAL_OR_EQUALIZING_PULSE) {
    // Having pulse, followed by ~33% horizontal distance after the previous pulse,
    // consider it first equalizing pulse right after the last vertical pulse
    return enterProcessingEqualizingSyncRegionState(pulse);
  }
}

inline void VideoSyncStateMachine::enterProcessingHorizontalRegionState(const VideoSyncPulse& pulse) {
  processedLinesCount_ = 0;
  setPreviousPulse(pulse, VideoSyncPulseType::HORIZONTAL_SYNC_PULSE); // Classify the current pulse as horizontal
  setNextState(PROCESSING_HORIZONTAL_REGION_STATE);
}

inline void VideoSyncStateMachine::enterProcessingEqualizingSyncRegionState(const VideoSyncPulse& pulse) {
  processedEqPulsesCount_ = 1;
  setPreviousPulse(pulse, VideoSyncPulseType::EQUALIZING_PULSE);
  setNextState(PROCESSING_EQUALIZING_SYNCH_REGION_STATE);
}

inline void VideoSyncStateMachine::enterProcessingVerticalSyncRegionState(const VideoSyncPulse& pulse, const bool isFirst) {
  // We can detect if it's a first or a second subsequent pulse only for vertical pulses,
  // because they have deterministic timings in compare to horizontal and equalizing pulses
  processedVSyncPulsesCount_ = isFirst ? 1 : 2;
  setPreviousPulse(pulse, VideoSyncPulseType::VERTICAL_PULSE);
  setNextState(PROCESSING_VERTICAL_SYNC_REGION_STATE);
}

}
