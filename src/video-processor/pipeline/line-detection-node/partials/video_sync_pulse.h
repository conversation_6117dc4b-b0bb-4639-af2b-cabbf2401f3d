#pragma once
#include "./video-sync-detector.h"

namespace IQVideoProcessor {

struct VideoSyncPulse: VideoSyncDetector::Result {
  TFloat absFallingFrontPosition{0};
  TFloat absRisingFrontPosition{0};
  TFloat absCenterPosition{0};

  static VideoSyncPulse fromSyncResult(const VideoSyncDetector::Result &r, const TFloat absolutePosition) {
    VideoSyncPulse vsp;
    vsp.fallingFrontPosition = r.fallingFrontPosition;
    vsp.risingFrontPosition = r.risingFrontPosition;
    vsp.centerPosition = r.centerPosition;
    vsp.width = r.width;
    vsp.absFallingFrontPosition = r.fallingFrontPosition + absolutePosition;
    vsp.absRisingFrontPosition = r.risingFrontPosition + absolutePosition;
    vsp.absCenterPosition = r.centerPosition + absolutePosition;
    return vsp;
  };
};

}
