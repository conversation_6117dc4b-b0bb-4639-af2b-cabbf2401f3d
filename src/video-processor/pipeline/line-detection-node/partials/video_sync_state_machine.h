#pragma once
#include "./video_standard_detector.h"
#include <functional>

namespace IQVideoProcessor {

class VideoSyncStateMachine {
public:
  enum EventType {
    FRAME_BEGIN = 0,
    EQUALIZATION_PULSE = 10,
    LINE_DETECTED = 20,
    EMPTY_LINE = 30,
    FRAME_END = 40,
    UNKNOWN_EVENT = 50
  };

  struct EventData {
    EventType type{UNKNOWN_EVENT};
    size_t frameWidth{0};            // For FRAME_BEGIN events
    size_t frameHeight{0};           // For FRAME_BEGIN events
    uint32_t pulseNumber{0};         // For EQUALIZATION_PULSE events
    uint32_t lineNumber{0};          // For LINE_DETECTED events
    TFloat fromPosition{0};          // Relative data start position
    size_t sampleCount{0};           // Number of samples in this segment
  };

  using EventCallback = std::function<void(const EventData&)>;

  explicit VideoSyncStateMachine(SampleRateType sampleRate);
  void initialize(const VideoStandardDetector::Result& detectedStandard, const EventCallback& eventCallback);
  void reset();
  void processNextSyncPulse(const VideoSyncPulse& pulse);

private:
  enum VideoSyncPulseType {
    UNKNOWN_PULSE = 0,
    HORIZONTAL_SYNC_PULSE = 10,
    EQUALIZING_PULSE = 20,
    HORIZONTAL_OR_EQUALIZING_PULSE = 25,
    VERTICAL_PULSE = 30,
    VERTICAL_LONG_PULSE = 35,
  };

  enum State {
    UNKNOWN_STATE,
    WAITING_FIRST_SYNC_PULSE_STATE,           // Initial state, looking for first sync pulse
    SYNCING_IN_PROGRESS_STATE,                // Sync pulses being classified
    PROCESSING_HORIZONTAL_REGION_STATE,       //
    PROCESSING_EQUALIZING_SYNCH_REGION_STATE, //
    PROCESSING_VERTICAL_SYNC_REGION_STATE,    //
  };

  SampleRateType sampleRate_;
  EventCallback eventCallback_;
  VideoStandardDetector::Result ds_{};
  SignalRangeEstimator rangeEstimator_{sampleRate_};

  State previousState_{UNKNOWN_STATE};
  State currentState_{UNKNOWN_STATE};

  VideoSyncPulse previousPulse_{};
  VideoSyncPulseType previousPulseType_{UNKNOWN_PULSE};
  size_t processedLinesCount_{0};
  size_t processedEqPulsesCount_{0};
  size_t processedVSyncPulsesCount_{0};

  inline void setNextState(State nextState);
  inline void setPreviousPulse(const VideoSyncPulse& pulse, VideoSyncPulseType pulseType);

  // State handling methods
  inline void handleWaitingFirstSyncPulse(const VideoSyncPulse& pulse, VideoSyncPulseType pulseType);
  inline void handleSyncingInProgress(const VideoSyncPulse& pulse, VideoSyncPulseType pulseType);

  // State entry methods
  inline void enterProcessingHorizontalRegionState(const VideoSyncPulse& pulse);
  inline void enterProcessingEqualizingSyncRegionState(const VideoSyncPulse& pulse);
  inline void enterProcessingVerticalSyncRegionState(const VideoSyncPulse& pulse, bool isFirst);

  // Helpers
  [[nodiscard]] inline VideoSyncPulseType estimatePulseType(const VideoSyncPulse& pulse) const;
};

}
