#pragma once
#include "../../../types.h"
#include "../../../../types.h"
#include "../../../utils/range/range.h"

namespace IQVideoProcessor {

// TODO Rename to SignalRangeClassificator
class SignalRangeEstimator {
public:
  explicit SignalRangeEstimator(SampleRateType sampleRate);
  ~SignalRangeEstimator() = default;

  [[nodiscard]] EstimatedPulseType estimatePulseByWidth(TFloat pulseWidth) const;
  [[nodiscard]] EstimatedDistanceType estimateSignalDistance(TFloat distance) const;
  [[nodiscard]] bool isHorizontalLineDistance(TFloat distance) const;
  [[nodiscard]] bool isHalfHorizontalLineDistance(TFloat distance) const;
  [[nodiscard]] bool is70PercentHorizontalLineDistance(TFloat distance) const;
  [[nodiscard]] bool is33PercentHorizontalLineDistance(TFloat distance) const;

protected:
  Range<TFloat> horizontalOrEqualizingPulseWidthRange_{}; // 1.5-7 microseconds
  Range<TFloat> verticalSyncPulseWidthRange_{}; // 26-30 microseconds
  Range<TFloat> verticalSyncLongPulseWidthRange_{}; // 440-500 microseconds
  Range<TFloat> horizontalLineDistanceRange_{}; // ~62.3-65.2 microseconds
  Range<TFloat> horizontalLineHalfDistanceRange_{}; // ~30.0-35.0 microseconds
  Range<TFloat> horizontalLine70PercentDistanceRange_{}; // ~42.0-46.0 microseconds
  Range<TFloat> horizontalLine33PercentDistanceRange_{}; // ~18.0-22.0 microseconds

  SampleRateType sampleRate_;
};

}